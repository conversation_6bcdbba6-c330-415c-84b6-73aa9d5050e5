# libRTU_RlyModbus.so.1.0.0 遥控遥调反校点逻辑分析报告

## 文件基本信息
- **文件路径**: D:\nari\TCP\libRTU_RlyModbus.so.1.0.0
- **文件大小**: 0x17300 (94976 字节)
- **基址**: 0x0
- **代码段大小**: 0xbee0
- **MD5**: aa54f58615fcb9dfa49156d132efaa63
- **SHA256**: 77141620eda63247a98e21eb220aaad66cab4e326de90e885858485bf8a554cb

## 核心功能模块分析

### 1. 遥控处理模块

#### 1.1 主要函数
- **RlyModbus_Proc_Control** (地址: 0x61fb)
  - 功能：处理遥控命令的核心函数
  - 参数：设备ID (wIedID)，消息缓冲区 (pbyMsgBuf)
  - 主要逻辑：验证命令参数，执行遥控操作，返回执行结果

#### 1.2 遥控命令处理流程
1. **命令验证**：
   - 检查设备运行时控制结构 (ptIedRtCtl)
   - 验证命令源ID和命令参数匹配性
   - 比较消息中的地址和数据与预期值

2. **命令执行**：
   - 构造响应消息 (byMsgBuf)
   - 设置消息头：长度(12)、类型(0)、设备ID
   - 设置功能码：64 (遥控命令)、-127 (0x81, 响应标识)
   - 复制命令序号和执行结果

3. **反校验证**：
   - 检查命令步骤标识 (byStep & 0xC0)
   - 0x80: 选择操作，重置定时器继续等待执行
   - 其他: 执行操作，停止定时器，标记命令完成

### 2. 遥调处理模块

#### 2.1 主要函数
- **RlyModbus_Send_HostCmd** (地址: 0x36b1)
  - 功能：发送主机命令，处理遥控遥调请求
  - 参数：端口号 (wPortNo)
  - 返回：消息长度

#### 2.2 遥调命令处理流程
1. **命令类型判断**：
   - byMsgBuf[4] == 64: 遥控命令 (单点操作)
   - byMsgBuf[4] == 66: 遥调命令 (多点操作)

2. **遥控处理** (功能码64)：
   - 解析命令：byCmd = byMsgBuf[10] & 0x3F
   - 解析步骤：byStep = byMsgBuf[10] & 0xC0
   - 支持命令类型：1(合闸), 9(分闸), 10(复归)
   - 反校验证：比较命令源、命令类型、序号

3. **遥调处理** (功能码66)：
   - 支持多点操作 (最多10个点)
   - 数据类型转换：支持整型、浮点型、BCD码等
   - 字节序处理：支持大端、小端、混合字节序

### 3. 命令确认处理模块

#### 3.1 主要函数
- **RlyModbus_Proc_CmdAck** (地址: 0x5f67)
  - 功能：处理命令确认响应
  - 参数：设备ID (wIedID)，消息缓冲区 (pbyMsgBuf)

#### 3.2 确认处理流程
1. **错误检测**：
   - 检查响应中的错误标识 (pbyMsgBuf[1] & 0x80)
   - 验证响应参数与发送命令的一致性

2. **响应构造**：
   - 遥控响应：固定12字节格式
   - 遥调响应：变长格式，包含原始命令数据

3. **状态更新**：
   - 清除命令控制标识
   - 停止超时定时器
   - 向队列发送响应消息

### 4. 反校点逻辑核心机制

#### 4.1 反校验证原理
遥控遥调的反校点逻辑采用"选择-执行-确认"三步骤机制：

1. **选择步骤** (byStep = 0x80)：
   - 主站发送选择命令
   - 装置验证命令合法性
   - 返回选择确认，启动超时定时器

2. **执行步骤** (byStep = 0x40)：
   - 主站发送执行命令
   - 装置验证与选择命令的一致性
   - 执行实际操作，返回执行结果

3. **撤销步骤** (byStep = 0xC0)：
   - 主站发送撤销命令
   - 装置取消之前的选择操作
   - 清除相关状态

#### 4.2 安全保护机制
1. **命令源验证**：确保执行命令来自同一主站
2. **序号校验**：防止重复执行或错误执行
3. **超时保护**：60秒超时自动撤销选择
4. **参数一致性**：严格校验命令参数匹配

### 5. 数据结构分析

#### 5.1 关键数据结构
- **ptIedRtCtl**: 设备运行时控制结构
- **ptCmdRtCtl**: 命令运行时控制结构 (偏移+24)
- **ptModbusCtl**: Modbus控制结构
- **tagPYkInfo**: 遥控信息结构
- **tagPYsInfo**: 遥调信息结构

#### 5.2 控制字段含义
- **ptCmdRtCtl + 12**: 命令状态字 (0xFFFF表示空闲)
- **ptCmdRtCtl + 16**: 命令源ID
- **ptCmdRtCtl + 20**: 命令序号
- **ptCmdRtCtl + 22-25**: 命令参数缓存

### 6. Modbus协议适配

#### 6.1 功能码映射
- **功能码5**: 单线圈操作
- **功能码6**: 单寄存器操作  
- **功能码16**: 多寄存器操作

#### 6.2 数据转换
- 支持多种数据类型：U16, I16, U32, I32, F32, F64, BCD等
- 字节序转换：支持4种不同的字节序排列
- CRC校验：使用CRC16校验确保数据完整性

## 7. 关键代码片段分析

### 7.1 遥控命令验证逻辑
```c
// RlyModbus_Proc_Control 函数中的关键验证逻辑
if ( *(_WORD *)(ptIedRtCtl + 36) != 0xFFFF
  && pbyMsgBuf[2] == *(_BYTE *)(ptCmdRtCtl + 22)
  && pbyMsgBuf[3] == *(_BYTE *)(ptCmdRtCtl + 23)
  && pbyMsgBuf[4] == *(_BYTE *)(ptCmdRtCtl + 24)
  && pbyMsgBuf[5] == *(_BYTE *)(ptCmdRtCtl + 25) )
{
    // 构造确认响应
    byMsgBuf[0] = 12;           // 消息长度
    byMsgBuf[1] = 0;            // 消息类型
    *(_WORD *)&byMsgBuf[2] = wIedID;  // 设备ID
    byMsgBuf[4] = 64;           // 功能码：遥控
    byMsgBuf[5] = -127;         // 响应标识(0x81)
    // ... 其他字段设置
}
```

### 7.2 反校步骤判断逻辑
```c
// RlyModbus_Send_HostCmd 函数中的步骤处理
byStep = byMsgBuf[10] & 0xC0;
if ( byStep == 0x80 )  // 选择步骤
{
    // 验证命令合法性，启动定时器
    Timer_StartTimer(ptCmdRtCtl, 60, 1);
}
else if ( byStep == 0xC0 )  // 撤销步骤
{
    // 清除命令状态，停止定时器
    *((_WORD *)ptCmdRtCtl + 6) = -1;
    Timer_StopTimer(ptCmdRtCtl);
}
```

### 7.3 数据类型转换处理
```c
// 遥调数据转换逻辑示例
if ( byCmd == 2 )  // 浮点数据
{
    f_dw.dwData = (byMsgBuf[wRead + 3] << 8) |
                  (byMsgBuf[wRead + 4] << 16) |
                  (byMsgBuf[wRead + 5] << 24) |
                  byMsgBuf[wRead + 2];
    v9 = f_dw.fData <= 0.0 ? f_dw.fData - 0.5 : f_dw.fData + 0.5;
    iDataValue = (int)v9;
}
else  // 整数数据
{
    iDataValue = (byMsgBuf[wRead + 3] << 8) | byMsgBuf[wRead + 2];
}
```

## 8. 错误处理机制

### 8.1 错误码定义
- **错误码0**: 操作成功
- **错误码1**: 命令序号不匹配
- **错误码2**: 不支持的命令类型
- **错误码8**: 设备忙碌或命令冲突

### 8.2 超时处理
- **选择超时**: 60秒后自动撤销选择状态
- **通信超时**: 根据端口配置的轮询间隔设置
- **定时器管理**: 使用Timer_StartTimer/Timer_StopTimer管理

### 8.3 状态恢复
```c
// 命令完成后的状态清理
*(_WORD *)(ptCmdRtCtl + 12) = -1;  // 清除命令状态
Timer_StopTimer(ptCmdRtCtl);       // 停止定时器
```

## 9. 通信协议细节

### 9.1 消息格式
**遥控命令格式**:
```
[长度][类型][设备ID][功能码][数据][校验]
 1字节 1字节  2字节   1字节   N字节  2字节
```

**遥调命令格式**:
```
[长度][类型][设备ID][功能码][点数][数据组][校验]
 1字节 1字节  2字节   1字节   1字节  6N字节  2字节
```

### 9.2 CRC校验
```c
wChkCode = CRC_CalcCrc16(pbySendBuf, wMsgLen);
pbySendBuf[wMsgLen++] = HIBYTE(wChkCode);
pbySendBuf[wMsgLen++] = wChkCode;
```

## 10. 性能优化特性

### 10.1 内存管理
- 使用栈上缓冲区减少动态分配
- 预分配固定大小的消息缓冲区(256字节)
- 避免频繁的内存拷贝操作

### 10.2 并发控制
- 使用互斥锁保护共享资源
- 命令状态机确保操作原子性
- 定时器机制防止死锁

### 10.3 缓存机制
- 命令参数缓存避免重复解析
- 模型信息缓存提高查找效率

## 总结

该库实现了完整的遥控遥调反校点逻辑，具有以下特点：

1. **安全性高**：多重验证机制确保操作安全
2. **兼容性强**：支持多种数据类型和字节序
3. **可靠性好**：超时保护和错误处理机制完善
4. **标准化**：遵循Modbus协议标准
5. **高性能**：优化的内存管理和并发控制

反校点机制有效防止了误操作，确保了电力系统操作的安全性和可靠性。通过"选择-执行-确认"的三步骤机制，实现了对关键操作的双重确认，符合电力行业的安全要求。

## 11. 函数调用关系图

```mermaid
graph TD
    A[RlyModbus主函数] --> B[RlyModbus_Proc]
    B --> C[RlyModbus_Recv]
    B --> D[RlyModbus_Send_HostCmd]
    B --> E[RlyModbus_Send_Polling]

    C --> F[Sio_HardRecv]
    C --> G[RlyModbus_Proc_Control]
    C --> H[RlyModbus_Proc_CmdAck]
    C --> I[RlyModbus_Proc_Set]

    D --> J[Que_ReadPortCmdFromQueue]
    D --> K[Pub_GetIedRtCtlByID]
    D --> L[RlyModbus_Search_Model]
    D --> M[Timer_StartTimer]
    D --> N[CRC_CalcCrc16]

    G --> O[Pub_GetIedRtCtlByID]
    G --> P[Que_SaveRspToQueue]
    G --> Q[Timer_ResetTimer]
    G --> R[Timer_StopTimer]

    H --> S[Pub_GetIedRtCtlByID]
    H --> T[Que_SaveRspToQueue]
    H --> U[Timer_StopTimer]
```

## 12. 遥控遥调处理流程图

```mermaid
sequenceDiagram
    participant 主站 as 主站系统
    participant RTU as RTU装置
    participant 设备 as 现场设备

    Note over 主站,设备: 遥控操作流程

    主站->>RTU: 1. 发送选择命令(0x80)
    RTU->>RTU: 验证命令合法性
    RTU->>主站: 返回选择确认
    RTU->>RTU: 启动60秒超时定时器

    主站->>RTU: 2. 发送执行命令(0x40)
    RTU->>RTU: 验证命令一致性
    RTU->>设备: 执行实际操作
    设备->>RTU: 返回操作结果
    RTU->>主站: 返回执行确认
    RTU->>RTU: 清除命令状态

    Note over 主站,设备: 遥调操作流程

    主站->>RTU: 1. 发送遥调选择(0x80)
    RTU->>RTU: 解析多点数据
    RTU->>主站: 返回选择确认

    主站->>RTU: 2. 发送遥调执行(0x40)
    RTU->>RTU: 数据类型转换
    RTU->>设备: 写入寄存器值
    设备->>RTU: 确认写入成功
    RTU->>主站: 返回执行结果

    Note over 主站,设备: 异常处理流程

    主站->>RTU: 发送撤销命令(0xC0)
    RTU->>RTU: 清除选择状态
    RTU->>主站: 返回撤销确认
```

## 13. 关键算法实现

### 13.1 字节序转换算法
```c
// 支持4种字节序转换模式
switch(ptYsInfo->byOrder) {
    case 0: // 正常字节序 (ABCD)
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        byTmpBuf[wMsgLen++] = iDataValue;
        break;
    case 1: // 交换字节序 (BADC)
        byTmpBuf[wMsgLen++] = iDataValue;
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        break;
    case 2: // 大端字节序 (DCBA)
        byTmpBuf[wMsgLen++] = HIBYTE(iDataValue);
        byTmpBuf[wMsgLen++] = BYTE2(iDataValue);
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        byTmpBuf[wMsgLen++] = iDataValue;
        break;
    case 3: // 小端字节序 (ABCD)
        byTmpBuf[wMsgLen++] = iDataValue;
        byTmpBuf[wMsgLen++] = BYTE1(iDataValue);
        byTmpBuf[wMsgLen++] = BYTE2(iDataValue);
        byTmpBuf[wMsgLen++] = HIBYTE(iDataValue);
        break;
}
```

### 13.2 浮点数转换算法
```c
// 浮点数到整数的转换，包含四舍五入
if ( f_dw.fData <= 0.0 )
    v9 = f_dw.fData - 0.5;  // 负数向下取整
else
    v9 = f_dw.fData + 0.5;  // 正数向上取整
iDataValue = (int)v9;
```

## 14. 配置参数说明

### 14.1 设备配置参数
- **设备地址**: 用于Modbus通信的从站地址
- **端口配置**: 通信端口的波特率、数据位等参数
- **超时设置**: 命令执行超时时间(默认60秒)
- **轮询间隔**: 数据采集的时间间隔

### 14.2 模型配置参数
- **遥控点配置**: 包含功能码、寄存器地址、操作值等
- **遥调点配置**: 包含数据类型、字节序、转换参数等
- **数据映射**: 逻辑地址到物理地址的映射关系

## 15. 调试和维护建议

### 15.1 日志记录
建议在关键函数中添加日志记录：
- 命令接收和发送日志
- 错误处理和异常情况日志
- 性能统计和监控日志

### 15.2 测试用例
- 正常遥控遥调操作测试
- 异常情况处理测试(超时、错误命令等)
- 并发操作测试
- 边界条件测试

### 15.3 性能监控
- 命令响应时间监控
- 内存使用情况监控
- 通信错误率统计
